<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>