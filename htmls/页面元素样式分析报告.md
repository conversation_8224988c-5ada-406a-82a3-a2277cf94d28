首页分析
整体布局采用简洁设计风格，以白色为主色调，搭配浅灰色和蓝色等元素。左侧是导航栏，右侧是主要内容展示区。

左侧导航栏：左上角有“天工”的品牌标识，为蓝绿渐变的图标加文字形式；下方是搜索框，显示“搜索（⌘ + k）”；导航项依次为“首页”（当前选中，图标为房子形状）、“新建项目”（图标为加号）、“项目”（展开后列出了一些项目名称）、“知识库”（图标为书本形状）。
右上角区域：有“邀请好友赚积分”（字体为橙色，带有一个小人举着双手的图标）、“积分显示”（显示“3000”积分，旁边有“积分充值”选项）、“用户名”（显示“朱晓兵”，右侧有一个向下的箭头）。
主要内容展示区：标题部分显示“天工超级智能体”，副标题为“智能创作的专家文档智能体”，字体较大且加粗，位于页面上方居中位置；功能区有输入框区域，提示“请输入文档的主题和需求，或上传文件，让文档智能体帮你撰写”，左侧有“智能体 | 文档模式”标签，输入框下方有“通用场景”（下拉菜单样式）和“联网”（开启状态）两个按钮，还有五种模式选项；用户案例展示区展示了三个案例。
知识库首页分析
页面整体布局简洁，采用白色背景，左侧为导航栏，右侧为主要内容展示区。

左侧导航栏：顶部左上角有“天工”的品牌标识；紧接着是搜索框，提示文字为“搜索（& + k）”；导航选项依次为“首页”“新建项目”“项目”（展开后有多个子项）、“知识库”。
右上角区域：有“邀请好友赚积分”和显示积分数量“3000”及“积分充值”选项；右上角显示用户头像和用户名“朱晓兵”，旁边有一个下拉箭头图标。
主要内容展示区：顶部有“上传文件”和“新建文件夹”两个按钮；左侧有一个浅蓝色背景的文件夹图标，标注为“文件夹01”，下方显示“0个文件”；右侧显示一个文件卡片，显示该文件在2分钟前上传，文件类型为PDF，文件名称为“【01】坚果云入门基础知识”，文件大小为757.62 KB ；右上角有“多选”和一个类似视图切换的图标。
项目详情页分析
界面主要分为左侧的对话区域和右侧的信息展示区域。

左侧对话区域：顶部标题栏左上角显示“品牌事件Agent交互流程图”，旁边有“智能体 | 文档模式”的标识；用户以蓝色对话框形式输入了一段需求；智能体以白色对话框形式回复；下方有“补充信息”栏，显示“已确认”，并有相关的选项和输入框提示，还有“通用场景”下拉菜单和“联网”选项。
右侧信息展示区域：顶部显示“Skywork虚拟机”；显示“MCP工具搜索网页 正在搜索网页 璇玑UI是什么”，下方有“搜索”按钮；罗列了多条与“璇玑UI”相关的搜索结果；右下角有“返回最新进展”按钮。
知识库文件夹详情页分析
界面左侧是导航栏，顶部是“天工”的标志。导航栏中有“搜索（⌘ + k）”的搜索框，下面依次是多个导航选项。界面右侧上方显示“知识库”标题，左侧有返回“文件夹01”的箭头，旁边是“上传文件”按钮。右上角有“邀请好友赚积分”“3000 积分充值”信息，右侧是用户头像和用户名“朱晓兵”。再往右有“多选”和一个图标按钮。界面中间显示了一个文件信息卡片，显示文件刚刚上传，文件类型为PDF，文件名为“【01】坚果云入门基础知识_4211”，下方有进度条，标注“解析中”。

知识库文件详情页分析
顶部导航栏：左上角显示“天工”字样和其图标；紧接着是“知识库”字样；右上角有“邀请好友赚积分”“3000”积分数值以及“积分充值”选项；最右侧是用户头像和用户名“朱晓兵”，旁边有一个向下的箭头图标。
文档标题栏：标题栏左侧有一个红色的“POP”标识；标题为“【01】坚果云入门基础知识”，旁边显示“1分钟前”；右侧有“下载”按钮和“+ 创建新项目”按钮。
文档主体内容：文档主体左上角是坚果云的图标和文字“坚果云 jianguoyun.com”；下方有一个蓝色标签“1 - 基础入门”；主标题为“坚果云快速向导”，英文为“Nutstore Quick Guide”；文档右下角有页码“1 / 16”，以及“自适应”选项。
