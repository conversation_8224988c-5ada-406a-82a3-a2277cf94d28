import React from 'react'

const Sidebar = () => {
  const menuItems = [
    {
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M3.375 6.07529L7.6 2.90029C9.025 1.85029 11 1.85029 12.4 2.90029L16.6 6.07529C17.55 6.80029 18.1 7.90029 18.1 9.07529V14.1753C18.1 16.2253 16.45 17.9003 14.375 17.9003H5.6C3.55 17.9003 1.875 16.2503 1.875 14.1753V9.07529C1.875 7.90029 2.425 6.77529 3.375 6.07529Z" clipRule="evenodd"/>
        </svg>
      ),
      label: '首页',
      active: true
    },
    {
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M6.04163 10C6.04163 9.65482 6.32145 9.375 6.66663 9.375H9.375V6.66675C9.375 6.32157 9.65482 6.04175 10 6.04175C10.3452 6.04175 10.625 6.32157 10.625 6.66675V9.375H13.3333C13.6785 9.375 13.9583 9.65482 13.9583 10C13.9583 10.3452 13.6785 10.625 13.3333 10.625H10.625V13.3334C10.625 13.6786 10.3452 13.9584 10 13.9584C9.65482 13.9584 9.375 13.6786 9.375 13.3334V10.625H6.66663C6.32145 10.625 6.04163 10.3452 6.04163 10Z"/>
          <path fillRule="evenodd" d="M1.69141 10.0013C1.69141 5.42943 5.42943 1.69141 10.0013 1.69141C14.5732 1.69141 18.3112 5.42943 18.3112 10.0013C18.3112 14.5732 14.5732 18.3112 10.0013 18.3112C5.42943 18.3112 1.69141 14.5732 1.69141 10.0013Z" clipRule="evenodd"/>
        </svg>
      ),
      label: '新建项目'
    },
    {
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M5.07498 11.0667H7.64998V17.0667C7.64998 18.4667 8.40832 18.7501 9.33332 17.7001L15.6417 10.5334C16.4167 9.65841 16.0916 8.93341 14.9166 8.93341H12.3417V2.93341C12.3417 1.53341 11.5833 1.25008 10.6583 2.30008L4.34998 9.46674C3.58332 10.3501 3.90832 11.0667 5.07498 11.0667Z"/>
        </svg>
      ),
      label: '项目'
    },
    {
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 1.04053C10.7029 1.03936 11.44 1.18489 12.0337 1.5101L16.4861 3.98647C17.1109 4.3326 17.6429 4.91446 18.0165 5.54808C18.3902 6.18183 18.6417 6.9282 18.6417 7.64158V12.3499C18.6417 13.0633 18.3902 13.8097 18.0165 14.4434C17.6429 15.077 17.1115 15.6585 16.4868 16.0047L12.0372 18.4794C11.4447 18.8068 10.7066 18.952 10.0042 18.952C9.30178 18.952 8.56367 18.8068 7.97273 18.4803L3.52227 16.005C2.89755 15.6589 2.36544 15.077 1.99186 14.4434C1.61818 13.8097 1.3667 13.0633 1.3667 12.3499V7.64158C1.3667 6.9282 1.61818 6.18183 1.99186 5.54808C2.36553 4.91436 2.8971 4.33271 3.52203 3.9866L7.97064 1.52071C8.56179 1.1893 9.29813 1.0417 10 1.04053Z" clipRule="evenodd"/>
        </svg>
      ),
      label: '知识库'
    }
  ]

  return (
    <div className="w-60 bg-sidebar-bg flex flex-col h-full">
      {/* Logo */}
      <div className="flex items-center gap-2 py-5 px-4">
        <div className="w-7 h-7 bg-gradient-to-r from-blue-500 to-purple-600 rounded flex items-center justify-center">
          <span className="text-white font-bold text-sm">天</span>
        </div>
        <span className="text-lg font-medium text-gray-800">天工</span>
      </div>

      {/* Search Bar */}
      <div className="px-4 mb-2">
        <div className="bg-gray-100 rounded-xl px-3 py-2 flex items-center text-sm text-gray-500 cursor-pointer hover:bg-gray-200 transition-colors">
          <svg className="w-4 h-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M2.12248 6.9183C3.1746 3.71123 6.17572 1.54928 9.55091 1.567C12.6826 1.57542 15.5035 3.46188 16.7075 6.35289C17.8987 9.21333 17.2769 12.5046 15.1327 14.7328L17.3879 16.988C17.6706 17.2711 17.6705 17.7297 17.3878 18.0128C17.1048 18.2955 16.6462 18.2954 16.3631 18.0127L15.2887 16.9382L14.0233 15.6729C13.9565 15.7205 13.8892 15.767 13.8213 15.8122C11.1244 17.6103 7.57774 17.55 4.93747 15.6301C2.20762 13.645 1.07035 10.1254 2.12248 6.9183Z" clipRule="evenodd"/>
          </svg>
          <span>搜索</span>
          <span className="ml-auto flex items-center gap-1">
            <span className="text-xs">(</span>
            <kbd className="px-1 py-0.5 text-xs bg-gray-200 rounded">⌘</kbd>
            <span className="text-xs">+</span>
            <kbd className="px-1 py-0.5 text-xs bg-gray-200 rounded">k</kbd>
            <span className="text-xs">)</span>
          </span>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-4">
        {menuItems.map((item, index) => (
          <div
            key={index}
            className={`sidebar-item ${item.active ? 'active' : ''}`}
          >
            {item.icon}
            <span className="ml-3">{item.label}</span>
          </div>
        ))}
      </nav>

      {/* Help Icon */}
      <div className="p-4">
        <div className="flex justify-end">
          <svg className="w-5 h-5 text-gray-400 cursor-pointer hover:text-gray-600" viewBox="0 0 21 20" fill="currentColor">
            <path d="M11.8676 14.5164C11.8676 14.7225 11.8065 14.924 11.692 15.0954C11.5774 15.2668 11.4147 15.4004 11.2242 15.4793C11.0338 15.5581 10.8242 15.5788 10.622 15.5386C10.4199 15.4983 10.2342 15.3991 10.0884 15.2533C9.94265 15.1076 9.84338 14.9219 9.80317 14.7197C9.76295 14.5175 9.78359 14.3079 9.86248 14.1175C9.94136 13.9271 10.0749 13.7643 10.2463 13.6498C10.4177 13.5352 10.6192 13.4741 10.8254 13.4741C11.1018 13.4741 11.3669 13.5839 11.5624 13.7794C11.7578 13.9748 11.8676 14.2399 11.8676 14.5164Z"/>
          </svg>
        </div>
      </div>
    </div>
  )
}

export default Sidebar
